using Autofac.Core;
using Core.CrossCuttingConcerns.Logging.FileLogger;
using Core.CrossCuttingConcerns.Logging;
using Core.Utilities.IoC;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.DependencyResolvers
{
    public class CoreModule : ICoreModule
    {
        public void Load(IServiceCollection serviceCollection)
        {
            // HTTP Context
            serviceCollection.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            // Company Context (Multi-tenant)
            serviceCollection.AddScoped<Core.Utilities.Security.CompanyContext.ICompanyContext, Core.Utilities.Security.CompanyContext.CompanyContext>();

            // Logging
            serviceCollection.AddSingleton<Stopwatch>();
            serviceCollection.AddSingleton<FileLoggerService>();
            serviceCollection.AddSingleton<PerformanceLoggerService>();
            serviceCollection.AddSingleton<ILogService, FileLoggerService>();
        }
    }
}
